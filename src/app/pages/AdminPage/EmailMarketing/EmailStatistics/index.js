import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Tabs } from "antd";

import Loading from "@component/Loading";

// Import custom hooks
import { useStatistics, useFilters } from "./hooks";

// Import components
import {
  HeaderSection,
  FilterSection,
  OverviewMetrics,
  PerformanceChart,
  DeviceChart,
  CampaignTable,
  ActivityTable
} from "./components";

import "../EmailMarketing.scss";
import "./EmailStatistics.scss";



const EmailStatistics = () => {
  const { t } = useTranslation();
  const location = useLocation();

  // Use custom hooks
  const { statisticsData, isLoading, fetchStatistics } = useStatistics();
  const {
    formFilter,
    campaigns,
    showDatePicker,
    minDate,
    maxDate,
    onSubmitFilter,
    onClearFilter,
    handleTimeRangeChange,
    setMinDate,
    setMaxDate,
    getCurrentFilters,
    TIME_RANGES
  } = useFilters(t);

  // Fetch statistics when URL changes
  useEffect(() => {
    const filters = getCurrentFilters();
    fetchStatistics(filters);
  }, [location.search]); // Remove dependencies to avoid cycle



  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container email-statistics-container">
        {/* Header Section */}
        <HeaderSection />

        {/* Filter Section */}
        <FilterSection
          formFilter={formFilter}
          campaigns={campaigns}
          showDatePicker={showDatePicker}
          minDate={minDate}
          maxDate={maxDate}
          onSubmitFilter={onSubmitFilter}
          onClearFilter={onClearFilter}
          handleTimeRangeChange={handleTimeRangeChange}
          setMinDate={setMinDate}
          setMaxDate={setMaxDate}
          TIME_RANGES={TIME_RANGES}
        />

        {/* Statistics Data */}
        {statisticsData && (
          <>
            {/* Overview Metrics */}
            <OverviewMetrics statisticsData={statisticsData} />

            {/* Charts and Tables */}
            <Tabs defaultActiveKey="1">
              <Tabs.TabPane tab={t("PERFORMANCE_OVER_TIME")} key="1">
                <PerformanceChart statisticsData={statisticsData} />
              </Tabs.TabPane>

              <Tabs.TabPane tab={t("CAMPAIGN_PERFORMANCE")} key="2">
                <CampaignTable statisticsData={statisticsData} />
              </Tabs.TabPane>

              {/*<Tabs.TabPane tab={t("DEVICE_BREAKDOWN")} key="3">*/}
              {/*  <DeviceChart statisticsData={statisticsData} />*/}
              {/*</Tabs.TabPane>*/}

              <Tabs.TabPane tab={t("RECENT_ACTIVITY")} key="4">
                <ActivityTable statisticsData={statisticsData} />
              </Tabs.TabPane>
            </Tabs>
          </>
        )}
      </div>
    </Loading>
  );
};

export default EmailStatistics;
