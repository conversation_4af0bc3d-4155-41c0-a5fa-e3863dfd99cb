// Test file to check imports
console.log('Testing EmailStatistics imports...');

try {
  // Test hooks
  const { useStatistics, useFilters } = require('./hooks');
  console.log('✅ Hooks imported successfully');
  
  // Test components
  const {
    HeaderSection,
    FilterSection,
    OverviewMetrics,
    PerformanceChart,
    CampaignTable,
    ActivityTable
  } = require('./components');
  console.log('✅ Components imported successfully');
  
  // Test utils
  const utils = require('./utils');
  console.log('✅ Utils imported successfully');
  
  console.log('🎉 All imports working correctly!');
} catch (error) {
  console.error('❌ Import error:', error.message);
  console.error('Stack:', error.stack);
}
