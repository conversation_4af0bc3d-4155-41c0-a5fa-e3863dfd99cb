.email-statistics-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles following FeedbackAnalysis pattern
  .email-marketing-info-card,
  .email-marketing-search-card,
  .email-marketing-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .email-marketing-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .email-marketing-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .email-marketing-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Filter section
  .email-marketing-search-card {
    .form-filter {
      width: 100%;

      .search-form-item {
        margin-bottom: 16px;
      }

      .filter-form__date-picker {
        width: 100%;
        padding: 9px 11px !important;
      }

      .search-buttons-row {
        margin-top: 8px;
      }

      .search-buttons {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
      }

      // Responsive styles
      @media (max-width: 768px) {
        .search-buttons {
          margin-top: 16px;
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }

  // Metric Cards
  .metric-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: var(--shadow-level-2);
      transform: translateY(-2px);
    }

    .ant-card-body {
      padding: 20px;
    }

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background-color: rgba(24, 144, 255, 0.1);

      .anticon {
        font-size: 20px;
        color: #1890ff;
      }
    }

    &__trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 12px;

      &.positive {
        color: #52c41a;
        background-color: rgba(82, 196, 26, 0.1);
      }

      &.negative {
        color: #ff4d4f;
        background-color: rgba(255, 77, 79, 0.1);
      }

      .trend-value {
        margin-left: 2px;
      }
    }

    &__content {
      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: var(--typo-colours-secondary-grey);
          margin-bottom: 8px;
          font-weight: 500;
        }

        .ant-statistic-content {
          font-size: 24px;
          font-weight: 600;
          line-height: 1.2;
        }
      }
    }

    // Color variants
    &--blue {
      .metric-card__icon {
        background-color: rgba(24, 144, 255, 0.1);
        .anticon {
          color: #1890ff;
        }
      }
    }

    &--green {
      .metric-card__icon {
        background-color: rgba(82, 196, 26, 0.1);
        .anticon {
          color: #52c41a;
        }
      }
    }

    &--orange {
      .metric-card__icon {
        background-color: rgba(250, 173, 20, 0.1);
        .anticon {
          color: #faad14;
        }
      }
    }

    &--purple {
      .metric-card__icon {
        background-color: rgba(114, 46, 209, 0.1);
        .anticon {
          color: #722ed1;
        }
      }
    }
  }

  // Overview metrics grid
  .overview-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  // Table styles
  .email-marketing-table-card {
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      margin-bottom: 16px;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: var(--background-light-background-2);
    }

    // Activity action styling
    .activity-action {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      .action-label {
        font-weight: 600;
      }

      &--opened {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
      }

      &--clicked {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a;
      }

      &--unsubscribed {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
    }

    // Rate badge styling
    .rate-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;

      &.rate-good {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a;
      }

      &.rate-medium {
        background-color: rgba(250, 173, 20, 0.1);
        color: #faad14;
      }

      &.rate-low {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
    }

    // Chart placeholder styling
    .chart-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      background-color: var(--background-light-background-1);
      border-radius: 8px;
      border: 2px dashed #d9d9d9;

      .chart-info {
        text-align: center;
        color: var(--typo-colours-secondary-grey);

        p {
          margin: 8px 0;
          font-size: 14px;

          &:first-child {
            font-weight: 600;
            font-size: 16px;
            color: var(--typo-colours-primary-black);
          }
        }
      }
    }

    // Performance summary styling
    .performance-summary {
      .chart-note {
        margin-top: 24px;
        text-align: center;

        p {
          color: var(--typo-colours-secondary-grey);
          font-size: 14px;
          margin: 0;
        }
      }
    }

    // Device breakdown styling
    .device-breakdown {
      .device-item {
        padding: 16px;
        border-radius: 8px;
        background-color: var(--background-light-background-1);
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--background-light-background-2);
          transform: translateY(-2px);
          box-shadow: var(--shadow-level-1);
        }

        .device-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .device-name {
            font-weight: 600;
            color: var(--typo-colours-primary-black);
            font-size: 14px;
          }

          .device-percentage {
            font-weight: 600;
            color: var(--typo-colours-secondary-grey);
            font-size: 14px;
          }
        }

        .device-count {
          margin-top: 8px;
          font-size: 12px;
          color: var(--typo-colours-secondary-grey);
          text-align: center;
        }
      }
    }
  }

  // Tabs styling
  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;

      &.ant-tabs-tab-active {
        font-weight: 600;
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    gap: 16px;

    .email-marketing-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
