import { useState, useCallback } from "react";
import { getEmailStatistics } from "@services/EmailMarketing";
import dayjs from "dayjs";

// Mock data fallback
const mockStatisticsData = {
  overview: {
    totalSent: 12500,
    totalOpened: 7800,
    totalClicked: 3200,
    totalUnsubscribed: 150,
    openRate: 62.4,
    clickRate: 25.6,
    unsubscribeRate: 1.2
  },
  campaignPerformance: [
    {
      id: "1",
      name: "Chiến d<PERSON>ch ch<PERSON>o mừng",
      sent: 5000,
      opened: 3500,
      clicked: 1800,
      unsubscribed: 50,
      openRate: 70,
      clickRate: 36,
      unsubscribeRate: 1
    },
    {
      id: "2",
      name: "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> khu<PERSON>ến mãi tháng 5",
      sent: 4500,
      opened: 2800,
      clicked: 900,
      unsubscribed: 75,
      openRate: 62.2,
      clickRate: 20,
      unsubscribeRate: 1.7
    },
    {
      id: "3",
      name: "<PERSON><PERSON><PERSON><PERSON> nhở gia hạn g<PERSON> d<PERSON> v<PERSON>",
      sent: 3000,
      opened: 1500,
      clicked: 500,
      unsubscribed: 25,
      openRate: 50,
      clickRate: 16.7,
      unsubscribeRate: 0.8
    }
  ],
  timeSeriesData: [
    { date: '2025-05-01', sent: 420, opened: 300, clicked: 150 },
    { date: '2025-05-02', sent: 380, opened: 250, clicked: 120 },
    { date: '2025-05-03', sent: 450, opened: 320, clicked: 180 },
    { date: '2025-05-04', sent: 500, opened: 380, clicked: 200 },
    { date: '2025-05-05', sent: 480, opened: 350, clicked: 190 },
    { date: '2025-05-06', sent: 520, opened: 400, clicked: 210 },
    { date: '2025-05-07', sent: 580, opened: 420, clicked: 230 },
    { date: '2025-05-08', sent: 600, opened: 450, clicked: 250 },
    { date: '2025-05-09', sent: 550, opened: 400, clicked: 220 },
    { date: '2025-05-10', sent: 590, opened: 430, clicked: 240 },
    { date: '2025-05-11', sent: 620, opened: 470, clicked: 260 },
    { date: '2025-05-12', sent: 580, opened: 420, clicked: 230 },
    { date: '2025-05-13', sent: 600, opened: 450, clicked: 240 },
    { date: '2025-05-14', sent: 650, opened: 500, clicked: 270 },
    { date: '2025-05-15', sent: 700, opened: 520, clicked: 290 },
  ],
  deviceData: [
    { type: 'Desktop', value: 45 },
    { type: 'Mobile', value: 40 },
    { type: 'Tablet', value: 15 },
  ],
  recentActivity: [
    {
      id: "1",
      campaign: "Chiến dịch chào mừng",
      email: "<EMAIL>",
      action: "opened",
      timestamp: "2025-05-15T10:30:00.000Z"
    },
    {
      id: "2",
      campaign: "Thông báo khuyến mãi tháng 5",
      email: "<EMAIL>",
      action: "clicked",
      timestamp: "2025-05-15T10:25:00.000Z"
    },
    {
      id: "3",
      campaign: "Nhắc nhở gia hạn gói dịch vụ",
      email: "<EMAIL>",
      action: "unsubscribed",
      timestamp: "2025-05-15T10:20:00.000Z"
    },
    {
      id: "4",
      campaign: "Chiến dịch chào mừng",
      email: "<EMAIL>",
      action: "opened",
      timestamp: "2025-05-15T10:15:00.000Z"
    },
    {
      id: "5",
      campaign: "Thông báo khuyến mãi tháng 5",
      email: "<EMAIL>",
      action: "clicked",
      timestamp: "2025-05-15T10:10:00.000Z"
    }
  ]
};

// API call function
const getStatisticsData = async (filters) => {
  console.log("Fetching statistics with filters:", filters);
  try {
    const response = await getEmailStatistics(filters);
    return response || mockStatisticsData;
  } catch (error) {
    console.error("Error fetching statistics:", error);
    return mockStatisticsData; // Fallback to mock data
  }
};

export const useStatistics = () => {
  const [statisticsData, setStatisticsData] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchStatistics = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      // Prepare filters for API
      const { fromDate, toDate } = filters;
      const preparedFilters = {
        ...filters,
        ...fromDate ? { fromDate: dayjs(fromDate).startOf("day").unix() } : {},
        ...toDate ? { toDate: dayjs(toDate).endOf("day").unix() } : {},
      };

      const dataResponse = await getStatisticsData(preparedFilters);
      if (dataResponse) {
        setStatisticsData(dataResponse);
      }
    } catch (err) {
      setError(err);
      console.error("Error in fetchStatistics:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback((filters) => {
    return fetchStatistics(filters);
  }, [fetchStatistics]);

  return {
    statisticsData,
    isLoading,
    error,
    fetchStatistics,
    refetch
  };
};
