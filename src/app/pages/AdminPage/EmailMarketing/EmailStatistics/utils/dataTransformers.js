// Transform time series data for line chart
export const transformTimeSeriesData = (timeSeriesData) => {
  const lineData = [];
  if (timeSeriesData && Array.isArray(timeSeriesData)) {
    timeSeriesData.forEach(item => {
      lineData.push({ date: item.date, value: item.sent, category: 'Đã gửi' });
      lineData.push({ date: item.date, value: item.opened, category: 'Đã mở' });
    });
  }
  return lineData;
};

// Format number with thousands separator
export const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Calculate percentage
export const calculatePercentage = (value, total) => {
  if (!total || total === 0) return 0;
  return ((value / total) * 100).toFixed(1);
};

// Get trend indicator
export const getTrendIndicator = (current, previous) => {
  if (!previous || previous === 0) return null;
  const change = ((current - previous) / previous) * 100;
  return {
    value: Math.abs(change).toFixed(1),
    direction: change >= 0 ? 'up' : 'down',
    isPositive: change >= 0
  };
};

// Transform overview metrics for display
export const transformOverviewMetrics = (overview, previousOverview = null) => {
  if (!overview) return [];

  const metrics = [
    {
      key: 'totalSent',
      title: 'TOTAL_SENT',
      value: overview.totalSent,
      icon: 'MailOutlined',
      color: 'blue',
      trend: previousOverview ? getTrendIndicator(overview.totalSent, previousOverview.totalSent) : null
    },
    {
      key: 'totalOpened',
      title: 'TOTAL_OPENED',
      value: overview.totalOpened,
      icon: 'EyeOutlined',
      color: 'green',
      trend: previousOverview ? getTrendIndicator(overview.totalOpened, previousOverview.totalOpened) : null
    },
    {
      key: 'openRate',
      title: 'OPEN_RATE',
      value: overview.openRate,
      suffix: '%',
      precision: 1,
      icon: 'PercentageOutlined',
      color: 'orange',
      trend: previousOverview ? getTrendIndicator(overview.openRate, previousOverview.openRate) : null
    },
    {
      key: 'totalClicked',
      title: 'TOTAL_CLICKED',
      value: overview.totalClicked,
      icon: 'CheckCircleOutlined',
      color: 'purple',
      trend: previousOverview ? getTrendIndicator(overview.totalClicked, previousOverview.totalClicked) : null
    }
  ];

  return metrics;
};

// Transform device data for pie chart
export const transformDeviceData = (deviceData) => {
  if (!deviceData || !Array.isArray(deviceData)) return [];
  return deviceData.map(item => ({
    ...item,
    percentage: item.value
  }));
};

// Transform campaign performance data
export const transformCampaignPerformance = (campaignPerformance) => {
  if (!campaignPerformance || !Array.isArray(campaignPerformance)) return [];
  
  return campaignPerformance.map(campaign => ({
    ...campaign,
    openRateFormatted: `${campaign.openRate}%`,
    clickRateFormatted: `${campaign.clickRate}%`,
    unsubscribeRateFormatted: `${campaign.unsubscribeRate}%`,
    sentFormatted: formatNumber(campaign.sent),
    openedFormatted: formatNumber(campaign.opened),
    clickedFormatted: formatNumber(campaign.clicked)
  }));
};

// Transform recent activity data
export const transformRecentActivity = (recentActivity) => {
  if (!recentActivity || !Array.isArray(recentActivity)) return [];
  
  return recentActivity.map(activity => ({
    ...activity,
    actionType: activity.action,
    actionColor: getActionColor(activity.action),
    actionIcon: getActionIcon(activity.action)
  }));
};

// Get action color based on action type
export const getActionColor = (action) => {
  switch (action) {
    case "opened":
      return "blue";
    case "clicked":
      return "green";
    case "unsubscribed":
      return "red";
    default:
      return "default";
  }
};

// Get action icon based on action type
export const getActionIcon = (action) => {
  switch (action) {
    case "opened":
      return "EyeOutlined";
    case "clicked":
      return "CheckCircleOutlined";
    case "unsubscribed":
      return "CloseCircleOutlined";
    default:
      return "MailOutlined";
  }
};
