// Line chart configuration for performance over time
export const getLineChartConfig = (data = []) => ({
  data,
  xField: 'date',
  yField: 'value',
  seriesField: 'category',
  yAxis: {
    label: {
      formatter: (v) => `${v}`,
    },
  },
  legend: {
    position: 'top',
  },
  smooth: true,
  point: {
    size: 4,
    shape: 'circle',
  },
  tooltip: {
    shared: true,
    showCrosshairs: true,
    formatter: (datum) => {
      return {
        name: datum.category,
        value: datum.value?.toLocaleString() || '0'
      };
    }
  },
  animation: {
    appear: {
      animation: 'path-in',
      duration: 1000,
    },
  },
  color: ['#1890ff', '#52c41a', '#faad14', '#f5222d'],
});

// Pie chart configuration for device breakdown
export const getPieChartConfig = (data = []) => ({
  data,
  angleField: 'value',
  colorField: 'type',
  radius: 0.8,
  label: {
    type: 'outer',
    content: '{name} {percentage}%',
  },
  tooltip: {
    formatter: (datum) => {
      return {
        name: datum.type,
        value: `${datum.value}%`
      };
    }
  },
  interactions: [
    {
      type: 'pie-legend-active',
    },
    {
      type: 'element-active',
    },
  ],
  legend: {
    position: 'bottom',
  },
  color: ['#1890ff', '#52c41a', '#faad14'],
});

// Column chart configuration for campaign comparison
export const getColumnChartConfig = (data = []) => ({
  data,
  xField: 'name',
  yField: 'value',
  seriesField: 'type',
  isGroup: true,
  columnStyle: {
    radius: [4, 4, 0, 0],
  },
  legend: {
    position: 'top',
  },
  tooltip: {
    shared: true,
    showMarkers: true,
  },
  animation: {
    appear: {
      animation: 'grow-in-y',
      duration: 1000,
    },
  },
  color: ['#1890ff', '#52c41a', '#faad14'],
});

// Area chart configuration for trend analysis
export const getAreaChartConfig = (data = []) => ({
  data,
  xField: 'date',
  yField: 'value',
  seriesField: 'category',
  smooth: true,
  areaStyle: {
    fillOpacity: 0.6,
  },
  legend: {
    position: 'top',
  },
  tooltip: {
    shared: true,
    showCrosshairs: true,
  },
  animation: {
    appear: {
      animation: 'wave-in',
      duration: 1500,
    },
  },
  color: ['#1890ff', '#52c41a'],
});

// Default chart theme colors
export const CHART_COLORS = {
  primary: '#1890ff',
  success: '#52c41a',
  warning: '#faad14',
  error: '#f5222d',
  purple: '#722ed1',
  cyan: '#13c2c2',
  orange: '#fa8c16',
  pink: '#eb2f96'
};

// Chart responsive configuration
export const getResponsiveConfig = () => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      responsive: true,
    },
    tooltip: {
      responsive: true,
    }
  },
  scales: {
    x: {
      responsive: true,
    },
    y: {
      responsive: true,
    }
  }
});
