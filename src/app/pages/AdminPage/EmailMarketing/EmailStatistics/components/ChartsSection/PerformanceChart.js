import React from "react";
import { Card, Empty, Row, Col, Statistic } from "antd";
import { useTranslation } from "react-i18next";

const PerformanceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Check if we have time series data
  const hasData = statisticsData?.timeSeriesData && statisticsData.timeSeriesData.length > 0;

  if (!hasData) {
    return (
      <Card className="email-marketing-table-card">
        <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
        <Empty
          description={t("NO_CHART_DATA")}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // Calculate totals from time series data
  const totals = statisticsData.timeSeriesData.reduce(
    (acc, item) => ({
      sent: acc.sent + (item.sent || 0),
      opened: acc.opened + (item.opened || 0),
      clicked: acc.clicked + (item.clicked || 0),
    }),
    { sent: 0, opened: 0, clicked: 0 }
  );

  const openRate = totals.sent > 0 ? ((totals.opened / totals.sent) * 100).toFixed(1) : 0;
  const clickRate = totals.sent > 0 ? ((totals.clicked / totals.sent) * 100).toFixed(1) : 0;

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
      <div className="performance-summary">
        <Row gutter={24}>
          <Col xs={24} sm={12} md={6}>
            <Statistic
              title={t("TOTAL_SENT")}
              value={totals.sent}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Statistic
              title={t("TOTAL_OPENED")}
              value={totals.opened}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Statistic
              title={t("OPEN_RATE")}
              value={openRate}
              suffix="%"
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Statistic
              title={t("CLICK_RATE")}
              value={clickRate}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
        </Row>
        <div className="chart-note">
          <p>{t("DATA_POINTS")}: {statisticsData.timeSeriesData.length}</p>
        </div>
      </div>
    </Card>
  );
};

export default PerformanceChart;
