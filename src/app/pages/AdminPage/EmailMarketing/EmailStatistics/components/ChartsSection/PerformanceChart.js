import React from "react";
import { Card } from "antd";
import { Line } from '@ant-design/plots';
import { useTranslation } from "react-i18next";
import { transformTimeSeriesData } from "../../utils/dataTransformers";
import { getLineChartConfig } from "../../utils/chartConfigs";

const PerformanceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Transform data for line chart
  const lineData = transformTimeSeriesData(statisticsData?.timeSeriesData);
  
  // Get chart configuration
  const lineConfig = getLineChartConfig(lineData);

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
      <Line {...lineConfig} />
    </Card>
  );
};

export default PerformanceChart;
