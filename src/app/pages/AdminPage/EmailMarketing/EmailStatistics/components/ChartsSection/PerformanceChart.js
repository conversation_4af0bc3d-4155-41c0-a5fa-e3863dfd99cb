import React from "react";
import { Card } from "antd";
import { Line } from '@ant-design/plots';
import { useTranslation } from "react-i18next";

const PerformanceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Transform data for line chart
  const lineData = [];
  if (statisticsData?.timeSeriesData) {
    statisticsData.timeSeriesData.forEach(item => {
      lineData.push({ date: item.date, value: item.sent, category: 'Đã gửi' });
      lineData.push({ date: item.date, value: item.opened, category: 'Đã mở' });
    });
  }

  // Line chart configuration
  const lineConfig = {
    data: lineData,
    xField: 'date',
    yField: 'value',
    seriesField: 'category',
    yAxis: {
      label: {
        formatter: (v) => `${v}`,
      },
    },
    legend: {
      position: 'top',
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
      <Line {...lineConfig} />
    </Card>
  );
};

export default PerformanceChart;
