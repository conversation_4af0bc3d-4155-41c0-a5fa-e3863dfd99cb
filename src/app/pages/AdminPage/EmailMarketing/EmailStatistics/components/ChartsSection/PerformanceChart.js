import React from "react";
import { Card } from "antd";
import { Line } from '@ant-design/plots';
import { useTranslation } from "react-i18next";

const PerformanceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Transform data for line chart
  const lineData = [];
  if (statisticsData?.timeSeriesData) {
    statisticsData.timeSeriesData.forEach(item => {
      lineData.push({ date: item.date, value: item.sent, category: 'Đã gửi' });
      lineData.push({ date: item.date, value: item.opened, category: 'Đã mở' });
    });
  }

  // Enhanced line chart configuration
  const lineConfig = {
    data: lineData,
    xField: 'date',
    yField: 'value',
    seriesField: 'category',
    xAxis: {
      type: 'time',
      tickCount: 5,
    },
    yAxis: {
      label: {
        formatter: (v) => v.toLocaleString(),
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
          },
        },
      },
    },
    legend: {
      position: 'top',
      itemSpacing: 20,
    },
    smooth: true,
    point: {
      size: 4,
      shape: 'circle',
      style: {
        fill: 'white',
        stroke: '#1890ff',
        lineWidth: 2,
      },
    },
    tooltip: {
      shared: true,
      showCrosshairs: true,
      crosshairs: {
        type: 'line',
      },
      formatter: (datum) => {
        return {
          name: datum.category,
          value: datum.value?.toLocaleString() || '0'
        };
      }
    },
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#52c41a'],
    theme: {
      geometries: {
        point: {
          circle: {
            active: {
              style: {
                r: 6,
                fillOpacity: 1,
                stroke: '#000',
                lineWidth: 2,
              },
            },
          },
        },
      },
    },
  };

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
      <Line {...lineConfig} />
    </Card>
  );
};

export default PerformanceChart;
