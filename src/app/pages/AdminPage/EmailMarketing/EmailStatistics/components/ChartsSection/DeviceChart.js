import React from "react";
import { Card } from "antd";
import { Pie } from '@ant-design/plots';
import { useTranslation } from "react-i18next";

const DeviceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Pie chart configuration
  const pieConfig = {
    data: statisticsData?.deviceData || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'pie-legend-active',
      },
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("DEVICE_BREAKDOWN")}</h3>
      <Pie {...pieConfig} />
    </Card>
  );
};

export default DeviceChart;
