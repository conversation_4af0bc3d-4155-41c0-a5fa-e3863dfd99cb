import React from "react";
import { Card } from "antd";
import { Pie } from '@ant-design/plots';
import { useTranslation } from "react-i18next";
import { transformDeviceData } from "../../utils/dataTransformers";
import { getPieChartConfig } from "../../utils/chartConfigs";

const DeviceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Transform data for pie chart
  const deviceData = transformDeviceData(statisticsData?.deviceData);
  
  // Get chart configuration
  const pieConfig = getPieChartConfig(deviceData);

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("DEVICE_BREAKDOWN")}</h3>
      <Pie {...pieConfig} />
    </Card>
  );
};

export default DeviceChart;
