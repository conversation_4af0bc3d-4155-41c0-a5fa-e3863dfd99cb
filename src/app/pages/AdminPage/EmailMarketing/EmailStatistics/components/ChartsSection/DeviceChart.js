import React from "react";
import { Card } from "antd";
import { Pie } from '@ant-design/plots';
import { useTranslation } from "react-i18next";

const DeviceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Enhanced pie chart configuration
  const pieConfig = {
    data: statisticsData?.deviceData || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    innerRadius: 0.4,
    label: {
      type: 'outer',
      content: '{name}\n{percentage}%',
      style: {
        fontSize: 12,
        fontWeight: 'bold',
      },
    },
    statistic: {
      title: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '14px',
          color: '#8c8c8c',
        },
        content: 'Total\nDevices',
      },
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#262626',
        },
        formatter: (datum, data) => {
          const total = data.reduce((sum, item) => sum + item.value, 0);
          return `${total}%`;
        },
      },
    },
    interactions: [
      {
        type: 'pie-legend-active',
      },
      {
        type: 'element-active',
      },
      {
        type: 'element-highlight',
      },
    ],
    legend: {
      position: 'bottom',
      itemSpacing: 20,
      marker: {
        symbol: 'circle',
      },
    },
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.type,
          value: `${datum.value}%`
        };
      }
    },
    animation: {
      appear: {
        animation: 'grow-in-y',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#52c41a', '#faad14'],
  };

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("DEVICE_BREAKDOWN")}</h3>
      <Pie {...pieConfig} />
    </Card>
  );
};

export default DeviceChart;
