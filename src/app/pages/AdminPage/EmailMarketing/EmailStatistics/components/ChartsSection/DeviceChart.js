import React from "react";
import { Card, Empty, Progress, Row, Col } from "antd";
import { useTranslation } from "react-i18next";

const DeviceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Check if we have device data
  const hasData = statisticsData?.deviceData && statisticsData.deviceData.length > 0;

  if (!hasData) {
    return (
      <Card className="email-marketing-table-card">
        <h3>{t("DEVICE_BREAKDOWN")}</h3>
        <Empty
          description={t("NO_CHART_DATA")}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // Calculate total for percentage
  const total = statisticsData.deviceData.reduce((sum, item) => sum + item.value, 0);

  // Define colors for different device types
  const colors = ['#1890ff', '#52c41a', '#faad14', '#722ed1', '#eb2f96'];

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("DEVICE_BREAKDOWN")}</h3>
      <div className="device-breakdown">
        <Row gutter={[16, 16]}>
          {statisticsData.deviceData.map((device, index) => {
            const percentage = total > 0 ? ((device.value / total) * 100).toFixed(1) : 0;
            const color = colors[index % colors.length];

            return (
              <Col xs={24} sm={12} key={device.type || index}>
                <div className="device-item">
                  <div className="device-header">
                    <span className="device-name">{device.type || device.name}</span>
                    <span className="device-percentage">{percentage}%</span>
                  </div>
                  <Progress
                    percent={parseFloat(percentage)}
                    strokeColor={color}
                    showInfo={false}
                    strokeWidth={8}
                  />
                  <div className="device-count">
                    {device.value?.toLocaleString() || '0'} {t("USERS")}
                  </div>
                </div>
              </Col>
            );
          })}
        </Row>
      </div>
    </Card>
  );
};

export default DeviceChart;
