import React from "react";
import { Card, Table } from "antd";
import { useTranslation } from "react-i18next";

const CampaignTable = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Define columns for campaign performance table
  const campaignColumns = [
    {
      title: t("CAMPAIGN_NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: t("SENT"),
      dataIndex: "sent",
      key: "sent",
      width: 100,
      sorter: (a, b) => a.sent - b.sent,
    },
    {
      title: t("OPENED"),
      dataIndex: "opened",
      key: "opened",
      width: 100,
      sorter: (a, b) => a.opened - b.opened,
    },
    {
      title: t("OPEN_RATE"),
      dataIndex: "openRate",
      key: "openRate",
      width: 120,
      render: (text) => `${text}%`,
      sorter: (a, b) => a.openRate - b.openRate,
    },
  ];

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("CAMPAIGN_PERFORMANCE")}</h3>
      <Table
        columns={campaignColumns}
        dataSource={statisticsData?.campaignPerformance || []}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default CampaignTable;
