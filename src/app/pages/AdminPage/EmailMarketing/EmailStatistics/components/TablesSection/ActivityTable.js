import React from "react";
import { Card, Table } from "antd";
import { EyeOutlined, CheckCircleOutlined, CloseCircleOutlined, MailOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { formatTimeDate } from "@common/functionCommons";

const ActivityTable = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Define columns for recent activity table
  const activityColumns = [
    {
      title: t("CAMPAIGN"),
      dataIndex: "campaign",
      key: "campaign",
      width: 200,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 200,
    },
    {
      title: t("ACTION"),
      dataIndex: "action",
      key: "action",
      width: 150,
      render: (text) => {
        let icon;
        let color;
        let label;

        switch (text) {
          case "opened":
            icon = <EyeOutlined />;
            color = "blue";
            label = t("OPENED");
            break;
          case "clicked":
            icon = <CheckCircleOutlined />;
            color = "green";
            label = t("CLICKED");
            break;
          case "unsubscribed":
            icon = <CloseCircleOutlined />;
            color = "red";
            label = t("UNSUBSCRIBED");
            break;
          default:
            icon = <MailOutlined />;
            color = "default";
            label = text;
        }

        return (
          <span className={`activity-action activity-action--${text}`} style={{ color }}>
            {icon} <span className="action-label">{label}</span>
          </span>
        );
      },
    },
    {
      title: t("TIMESTAMP"),
      dataIndex: "timestamp",
      key: "timestamp",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
  ];

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("RECENT_ACTIVITY")}</h3>
      <Table
        columns={activityColumns}
        dataSource={statisticsData?.recentActivity || []}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default ActivityTable;
