import React from "react";
import { useTranslation } from "react-i18next";
import MetricCard from "./shared/MetricCard";

const OverviewMetrics = ({ statisticsData, isLoading = false }) => {
  const { t } = useTranslation();

  if (!statisticsData?.overview) {
    return null;
  }

  const { overview } = statisticsData;

  // Define metrics with enhanced data
  const metrics = [
    {
      title: "TOTAL_SENT",
      value: overview.totalSent,
      icon: "MailOutlined",
      color: "blue",
      trend: null // Can be enhanced later with previous period data
    },
    {
      title: "TOTAL_OPENED",
      value: overview.totalOpened,
      icon: "EyeOutlined",
      color: "green",
      trend: null
    },
    {
      title: "OPEN_RATE",
      value: overview.openRate,
      icon: "PercentageOutlined",
      color: "orange",
      suffix: "%",
      precision: 1,
      trend: null
    },
    {
      title: "TOTAL_CLICKED",
      value: overview.totalClicked,
      icon: "CheckCircleOutlined",
      color: "purple",
      trend: null
    }
  ];

  return (
    <div className="overview-metrics-grid">
      {metrics.map((metric, index) => (
        <MetricCard
          key={metric.title}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          color={metric.color}
          trend={metric.trend}
          suffix={metric.suffix}
          precision={metric.precision}
          loading={isLoading}
        />
      ))}
    </div>
  );
};

export default OverviewMetrics;
