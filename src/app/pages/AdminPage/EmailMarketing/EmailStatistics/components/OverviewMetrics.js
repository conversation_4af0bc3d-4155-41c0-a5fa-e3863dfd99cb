import React from "react";
import { Card, Row, Col, Statistic } from "antd";
import { MailOutlined, EyeOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const OverviewMetrics = ({ statisticsData }) => {
  const { t } = useTranslation();

  if (!statisticsData?.overview) {
    return null;
  }

  const { overview } = statisticsData;

  return (
    <Card className="email-marketing-info-card">
      <Row gutter={24}>
        <Col xs={24} sm={12} md={8}>
          <Statistic
            title={t("TOTAL_SENT")}
            value={overview.totalSent}
            prefix={<MailOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Statistic
            title={t("TOTAL_OPENED")}
            value={overview.totalOpened}
            prefix={<EyeOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Statistic
            title={t("OPEN_RATE")}
            value={overview.openRate}
            suffix="%"
            precision={1}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default OverviewMetrics;
