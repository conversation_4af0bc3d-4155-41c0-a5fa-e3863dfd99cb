import React from "react";
import { Card, Row, Col, Select, DatePicker } from "antd";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import { BUTTON } from "@constant";

const FilterSection = ({
  formFilter,
  campaigns,
  showDatePicker,
  minDate,
  maxDate,
  onSubmitFilter,
  onClearFilter,
  handleTimeRangeChange,
  setMinDate,
  setMaxDate,
  TIME_RANGES
}) => {
  const { t } = useTranslation();

  return (
    <Card className="email-marketing-search-card">
      <AntForm 
        form={formFilter} 
        layout="horizontal" 
        size="large" 
        className="form-filter" 
        onFinish={onSubmitFilter}
      >
        <Row gutter={24} align="middle" className="grow">
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name="campaign" className="search-form-item" initialValue="all">
              <Select
                options={campaigns}
                placeholder={t("SELECT_CAMPAIGN")}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  option?.label?.toLowerCase().includes(input.toLowerCase())
                }
              />
            </AntForm.Item>
          </Col>

          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name="time" className="search-form-item">
              <Select
                placeholder={t("SELECT_TIME_RANGE")}
                onChange={handleTimeRangeChange}
                allowClear
              >
                {TIME_RANGES.map(range => (
                  <Select.Option key={range.value} value={range.value}>
                    {t(range.label)}
                  </Select.Option>
                ))}
              </Select>
            </AntForm.Item>
          </Col>
          
          {showDatePicker && (
            <>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item
                  name="fromDate"
                  className="search-form-item"
                  rules={[
                    () => ({
                      validator(_, value) {
                        if (!!value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                      },
                    }),
                  ]}
                >
                  <DatePicker
                    placeholder={t("SELECT_FROM_DATE")}
                    size="large"
                    className="filter-form__date-picker"
                    format="DD/MM/YYYY"
                    maxDate={maxDate}
                    onChange={setMinDate}
                  />
                </AntForm.Item>
              </Col>
              
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item
                  name="toDate"
                  className="search-form-item"
                  rules={[
                    () => ({
                      validator(_, value) {
                        if (!!value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                      },
                    }),
                  ]}
                >
                  <DatePicker
                    placeholder={t("SELECT_TO_DATE")}
                    size="large"
                    className="filter-form__date-picker"
                    format="DD/MM/YYYY"
                    minDate={minDate}
                    onChange={setMaxDate}
                  />
                </AntForm.Item>
              </Col>
            </>
          )}
        </Row>
        
        <Row justify="end" className="search-buttons-row">
          <Col>
            <div className="search-buttons">
              <AntButton type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                {t("CLEAR")}
              </AntButton>
              <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit">
                {t("APPLY")}
              </AntButton>
            </div>
          </Col>
        </Row>
      </AntForm>
    </Card>
  );
};

export default FilterSection;
