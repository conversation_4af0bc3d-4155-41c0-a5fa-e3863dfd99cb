.exam-project-content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .exam-project-header {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .exam-project-header__title {
      font-size: 24px;
      font-weight: 600;
    }

    .exam-project-header__folder {
      display: flex;
      gap: 8px;

      .exam-project-header__folder-icon {
        display: flex;
        flex-shrink: 0;
        align-items: center;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }

  }

  .exam-project__submit {
    display: flex;
    flex-direction: row;
    background-color: var(--primary-colours-blue-navy-light-2);
    padding: 16px;
    border-radius: 8px;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .ant-form-item {
      margin-bottom: 0;
    }

    .exam-project__submit-button {
      margin-left: auto;
    }
  }

  .exam-project-collapse {
    border-radius: 8px;
    background-color: var(--white);
    box-shadow: var(--shadow-level-2);

    > .ant-collapse-item {
      > .ant-collapse-header {
        padding: 16px 32px;
        border-bottom: 1px solid var(--primary-colours-blue-navy);
        cursor: default;
        border-radius: 8px 8px 0 0;
        background-color: var(--white);

        .ant-collapse-expand-icon {
          display: none;
        }

        .ant-collapse-header-text {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          color: var(--primary-colours-blue-navy);
          text-transform: uppercase;
        }
      }

      > .ant-collapse-content {

        > .ant-collapse-content-box {
          padding: 0;

          > * {
            padding: 24px 32px;
          }
        }
      }
    }
  }
}